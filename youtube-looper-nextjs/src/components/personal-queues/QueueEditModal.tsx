'use client'

import { useState, useEffect } from 'react'
import { PersonalQueue, QueueItem } from '@/lib/types/queue'
import { VideoMetadata } from '@/lib/types/video'
import { firebaseService } from '@/lib/services/firebase'
import { useAuth } from '@/hooks/useAuth'
import { formatDuration } from '@/lib/utils/format'
import { DragDropContext, Droppable, Draggable, DropResult } from '@hello-pangea/dnd'
import { VideoSearchInput } from '../search/VideoSearchInput'

interface QueueEditModalProps {
  queue: PersonalQueue
  isOpen: boolean
  onClose: () => void
  onSave: (updatedQueue: PersonalQueue) => void
}

export function QueueEditModal({ queue, isOpen, onClose, onSave }: QueueEditModalProps) {
  const { user } = useAuth()
  const [title, setTitle] = useState(queue.metadata.title || '')
  const [description, setDescription] = useState(queue.metadata.description || '')
  const [tags, setTags] = useState(queue.metadata.tags?.join(', ') || '')
  const [videos, setVideos] = useState<QueueItem[]>(queue.queueData.items)
  const [isSaving, setIsSaving] = useState(false)


  useEffect(() => {
    if (isOpen) {
      setTitle(queue.metadata.title || '')
      setDescription(queue.metadata.description || '')
      setTags(queue.metadata.tags?.join(', ') || '')
      setVideos([...queue.queueData.items])
    }
  }, [isOpen, queue])

  const handleSave = async () => {
    if (!user || isSaving) return

    setIsSaving(true)
    try {
      // Update metadata
      const updatedMetadata = {
        ...queue.metadata,
        title: title.trim(),
        description: description.trim(),
        tags: tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0),
        videoCount: videos.length,
        totalDuration: videos.reduce((total, video) => total + (video.duration || 0), 0),
        firstVideoThumbnail: videos[0]?.thumbnail || '',
        lastModified: Date.now(),
      }

      // Update queue content
      const updatedQueueData = {
        ...queue.queueData,
        items: videos.map((video, index) => ({
          ...video,
          queueIndex: index,
        })),
      }

      // Save to Firebase
      const metadataSuccess = await firebaseService.updatePersonalQueueMetadata(queue.id, updatedMetadata)
      const contentSuccess = await firebaseService.updatePersonalQueueContent(queue.id, updatedQueueData)

      if (metadataSuccess && contentSuccess) {
        const updatedQueue: PersonalQueue = {
          ...queue,
          metadata: updatedMetadata,
          queueData: updatedQueueData,
          lastModified: Date.now(),
        }
        
        onSave(updatedQueue)
        onClose()
        console.log('✅ Queue updated successfully')
      } else {
        console.error('Failed to update queue')
      }
    } catch (error) {
      console.error('Error saving queue:', error)
    } finally {
      setIsSaving(false)
    }
  }

  const handleDragEnd = (result: DropResult) => {
    if (!result.destination) return

    const items = Array.from(videos)
    const [reorderedItem] = items.splice(result.source.index, 1)
    items.splice(result.destination.index, 0, reorderedItem)

    setVideos(items)
  }

  const removeVideo = (index: number) => {
    setVideos(videos.filter((_, i) => i !== index))
  }



  const addVideoToQueue = (video: VideoMetadata) => {
    const newQueueItem: QueueItem = {
      ...video,
      addedAt: Date.now(),
      queueIndex: videos.length,
      timeframes: [],
      loopSettings: {
        videoLoopCount: 1,
        loopMode: 'whole-video-plus-timeframes'
      }
    }
    setVideos([...videos, newQueueItem])
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-dark-900 rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-white/10">
          <h2 className="text-xl font-bold text-white">Edit Queue</h2>
          <button
            onClick={onClose}
            className="p-2 text-dark-400 hover:text-white hover:bg-white/10 rounded-lg transition-all duration-200"
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          {/* Basic Info */}
          <div className="space-y-4 mb-6">
            <div>
              <label className="block text-sm font-medium text-dark-300 mb-2">
                Queue Title
              </label>
              <input
                type="text"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                className="w-full px-4 py-2 bg-dark-800 border border-white/10 rounded-lg text-white placeholder-dark-400 focus:outline-none focus:ring-2 focus:ring-primary-500/50 focus:border-primary-500"
                placeholder="Enter queue title..."
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-dark-300 mb-2">
                Description
              </label>
              <textarea
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                rows={3}
                className="w-full px-4 py-2 bg-dark-800 border border-white/10 rounded-lg text-white placeholder-dark-400 focus:outline-none focus:ring-2 focus:ring-primary-500/50 focus:border-primary-500 resize-none"
                placeholder="Enter queue description..."
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-dark-300 mb-2">
                Tags (comma-separated)
              </label>
              <input
                type="text"
                value={tags}
                onChange={(e) => setTags(e.target.value)}
                className="w-full px-4 py-2 bg-dark-800 border border-white/10 rounded-lg text-white placeholder-dark-400 focus:outline-none focus:ring-2 focus:ring-primary-500/50 focus:border-primary-500"
                placeholder="music, rock, favorites..."
              />
            </div>
          </div>

          {/* Add Videos Section */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-white mb-4">Add Videos</h3>
            <VideoSearchInput
              onVideoSelect={addVideoToQueue}
              placeholder="Search for videos to add..."
            />
          </div>

          {/* Videos List */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-white">
                Videos ({videos.length})
              </h3>
              <span className="text-sm text-dark-400">
                Total: {formatDuration(videos.reduce((total, video) => total + (video.duration || 0), 0))}
              </span>
            </div>

            {videos.length === 0 ? (
              <div className="text-center py-8 text-dark-400">
                <p>No videos in this queue</p>
                <p className="text-sm mt-1">Search and add videos above</p>
              </div>
            ) : (
              <DragDropContext onDragEnd={handleDragEnd}>
                <Droppable droppableId="videos">
                  {(provided) => (
                    <div
                      {...provided.droppableProps}
                      ref={provided.innerRef}
                      className="space-y-2"
                    >
                      {videos.map((video, index) => (
                        <Draggable key={video.id} draggableId={video.id} index={index}>
                          {(provided, snapshot) => (
                            <div
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              {...provided.dragHandleProps}
                              className={`flex items-center gap-3 p-3 bg-dark-800 rounded-lg border border-white/10 transition-all duration-200 ${
                                snapshot.isDragging ? 'shadow-lg scale-105' : 'hover:bg-dark-700'
                              }`}
                            >
                              <div className="flex-shrink-0 w-16 h-12 bg-dark-700 rounded overflow-hidden">
                                {video.thumbnail && (
                                  <img
                                    src={video.thumbnail}
                                    alt={video.title}
                                    className="w-full h-full object-cover"
                                  />
                                )}
                              </div>
                              <div className="flex-1 min-w-0">
                                <h4 className="text-white font-medium truncate">{video.title}</h4>
                                <p className="text-sm text-dark-400 truncate">{video.channel || 'Unknown Channel'}</p>
                                <p className="text-xs text-dark-500">{formatDuration(video.duration || 0)}</p>
                              </div>
                              <button
                                onClick={() => removeVideo(index)}
                                className="p-2 text-dark-400 hover:text-red-400 hover:bg-red-400/10 rounded-lg transition-all duration-200"
                              >
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                  <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
                                </svg>
                              </button>
                            </div>
                          )}
                        </Draggable>
                      ))}
                      {provided.placeholder}
                    </div>
                  )}
                </Droppable>
              </DragDropContext>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end gap-3 p-6 border-t border-white/10">
          <button
            onClick={onClose}
            className="px-6 py-2 text-dark-400 hover:text-white hover:bg-white/10 rounded-lg transition-all duration-200"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            disabled={isSaving || !title.trim()}
            className="btn-primary px-6 py-2 disabled:opacity-50"
          >
            {isSaving ? 'Saving...' : 'Save Changes'}
          </button>
        </div>
      </div>
    </div>
  )
}
