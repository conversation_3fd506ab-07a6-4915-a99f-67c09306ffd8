'use client'

import { useState, useEffect } from 'react'
import { SearchInput } from './SearchInput'
import { SearchResults } from './SearchResults'
import { youtubeService } from '@/lib/services/youtube'
import { VideoSearchResult, PaginatedSearchResult } from '@/lib/types/video'
import { useAuth } from '@/hooks/useAuth'

import { useDraftQueue } from '@/hooks/useDraftQueue'
import { DraftQueue } from '@/components/draft-queue/DraftQueue'
import { QueueCreationForm } from '@/components/draft-queue/QueueCreationForm'

export function SearchView() {
  const { user, isAuthenticated } = useAuth()
  const {
    draftItems,
    draftCount,
    isCreationMode,
    isEditMode,
    enterCreationMode,
    exitCreationMode
  } = useDraftQueue()

  // Search state
  const [searchQuery, setSearchQuery] = useState('')
  const [isSearching, setIsSearching] = useState(false)
  const [isLoadingMore, setIsLoadingMore] = useState(false)
  const [searchResults, setSearchResults] = useState<VideoSearchResult[]>([])
  const [error, setError] = useState<string | null>(null)
  const [paginationData, setPaginationData] = useState<{
    nextPageToken?: string
    prevPageToken?: string
    totalResults: number
    currentPage: number
    hasNextPage: boolean
    hasPrevPage: boolean
  } | null>(null)

  // Automatically enter creation mode when SearchView becomes active (unless already in edit mode)
  useEffect(() => {
    if (!isCreationMode && !isEditMode) {
      enterCreationMode()
    }
  }, [isCreationMode, isEditMode, enterCreationMode])

  const handleSearch = async (query: string, pageToken?: string) => {
    if (!query.trim()) {
      setSearchResults([])
      setError(null)
      setPaginationData(null)
      return
    }

    if (pageToken) {
      setIsLoadingMore(true)
    } else {
      setIsSearching(true)
      setSearchQuery(query)
      setSearchResults([]) // Clear results for new search
      setPaginationData(null)
    }
    setError(null)

    try {
      console.log('🔍 Searching YouTube for:', query, pageToken ? `(page token: ${pageToken})` : '')
      const result = await youtubeService.searchVideosPaginated(query, 20, pageToken)

      if (pageToken) {
        // Append results for pagination, but deduplicate by ID
        setSearchResults(prev => {
          const existingIds = new Set(prev.map(item => item.id))
          const newResults = result.results.filter(item => !existingIds.has(item.id))
          return [...prev, ...newResults]
        })
      } else {
        // Replace results for new search
        setSearchResults(result.results)
      }

      setPaginationData({
        nextPageToken: result.nextPageToken,
        prevPageToken: result.prevPageToken,
        totalResults: result.totalResults,
        currentPage: result.currentPage,
        hasNextPage: result.hasNextPage,
        hasPrevPage: result.hasPrevPage
      })

      console.log('✅ Found', result.results.length, 'videos', pageToken ? '(loaded more)' : '')
    } catch (error: any) {
      console.error('❌ Search error:', error)
      setError(error.message || 'Failed to search videos')
      if (!pageToken) {
        setSearchResults([])
        setPaginationData(null)
      }
    } finally {
      if (pageToken) {
        setIsLoadingMore(false)
      } else {
        setIsSearching(false)
      }
    }
  }

  const handleLoadMore = () => {
    if (paginationData?.hasNextPage && paginationData.nextPageToken && searchQuery) {
      handleSearch(searchQuery, paginationData.nextPageToken)
    }
  }

  return (
    <div className="space-y-6">
      {/* Queue Creation Mode Header */}
      {isCreationMode && (
        <div className="glassmorphism rounded-2xl p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-10 h-10 bg-primary-500 rounded-full flex items-center justify-center text-white font-bold text-lg">
                +
              </div>
              <div>
                <h3 className="text-lg font-semibold text-white">Creating New Queue</h3>
                <p className="text-dark-300">Add videos to create your new queue</p>
              </div>
            </div>
            <button
              onClick={exitCreationMode}
              className="p-2 text-dark-300 hover:text-white transition-colors"
              title="Cancel"
            >
              <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
              </svg>
            </button>
          </div>
        </div>
      )}

      {/* Search Section (show when in creation or edit mode) */}
      {(isCreationMode || isEditMode) && (
        <>
          {/* Search Header */}
          <div className="glassmorphism rounded-2xl p-6">
            <div className="text-center mb-6">
              <h1 className="text-2xl font-bold text-white mb-2">
                {isEditMode ? 'Edit Queue - Search Videos' : 'Search YouTube Videos'}
              </h1>
              <p className="text-dark-300">
                {isEditMode ? 'Find and add more videos to your queue' : 'Find and add videos to your queue'}
              </p>
            </div>

            <SearchInput
              onSearch={handleSearch}
              isLoading={isSearching}
              placeholder="Search for videos, artists, or songs..."
            />
          </div>

          {/* Error Message */}
          {error && (
            <div className="glassmorphism rounded-2xl p-6">
              <div className="flex items-center space-x-3 text-red-400">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"/>
                </svg>
                <span>{error}</span>
              </div>
            </div>
          )}

          {/* Search Results */}
          <SearchResults
            results={searchResults}
            isLoading={isSearching}
            isLoadingMore={isLoadingMore}
            query={searchQuery}
            error={error}
            paginationData={paginationData}
            onLoadMore={handleLoadMore}
          />
        </>
      )}

      {/* Draft Queue Display (show when in creation or edit mode) - MOVED TO BOTTOM */}
      {(isCreationMode || isEditMode) && <DraftQueue />}

      {/* Queue Creation/Edit Form (show when in creation or edit mode) - MOVED TO BOTTOM */}
      {(isCreationMode || isEditMode) && (
        <QueueCreationForm />
      )}
    </div>
  )
}
