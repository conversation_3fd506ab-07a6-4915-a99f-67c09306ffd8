'use client'

import Image from 'next/image'
import { useEffect, useRef, useCallback } from 'react'
import { VideoSearchResult } from '@/lib/types/video'
import { useQueue } from '@/hooks/useQueue'
import { useDraftQueue } from '@/hooks/useDraftQueue'
import { youtubeService } from '@/lib/services/youtube'
import { formatDuration, parseYouTubeDuration, formatNumber } from '@/lib/utils/format'

interface SearchResultsProps {
  results: VideoSearchResult[]
  isLoading: boolean
  query: string
  error?: string | null
  paginationData?: {
    nextPageToken?: string
    prevPageToken?: string
    totalResults: number
    currentPage: number
    hasNextPage: boolean
    hasPrevPage: boolean
  } | null
  onLoadMore?: () => void
}

export function SearchResults({ results, isLoading, query, error, paginationData, onLoadMore }: SearchResultsProps) {
  const { addVideo } = useQueue()
  const { addToDraft, isInDraft, isCreationMode, isEditMode } = useDraftQueue()
  const scrollContainerRef = useRef<HTMLDivElement>(null)
  const isLoadingMore = useRef(false)

  // Handle scroll to bottom detection
  const handleScroll = useCallback(() => {
    const container = scrollContainerRef.current
    if (!container || isLoadingMore.current || !paginationData?.hasNextPage || !onLoadMore) {
      return
    }

    const { scrollTop, scrollHeight, clientHeight } = container
    const threshold = 100 // Load more when 100px from bottom

    if (scrollTop + clientHeight >= scrollHeight - threshold) {
      isLoadingMore.current = true
      onLoadMore()
    }
  }, [paginationData?.hasNextPage, onLoadMore])

  // Set up scroll listener
  useEffect(() => {
    const container = scrollContainerRef.current
    if (!container) return

    container.addEventListener('scroll', handleScroll, { passive: true })
    return () => container.removeEventListener('scroll', handleScroll)
  }, [handleScroll])

  // Reset loading flag when loading state changes
  useEffect(() => {
    if (!isLoading) {
      isLoadingMore.current = false
    }
  }, [isLoading])

  const handleAddToQueue = (result: VideoSearchResult) => {
    try {
      const videoMetadata = youtubeService.searchResultToVideoMetadata(result)

      if (isCreationMode || isEditMode) {
        // Add to draft queue when in creation or edit mode (with default loop count of 1)
        const draftId = addToDraft(videoMetadata)
        if (draftId) {
          console.log('✅ Added to draft queue:', videoMetadata.title, 'Draft ID:', draftId)
        }
      } else {
        // Add to main queue when not in creation or edit mode
        addVideo(videoMetadata)
        console.log('✅ Added to queue:', videoMetadata.title)
      }
    } catch (error) {
      console.error('❌ Failed to add video:', error)
    }
  }
  if (isLoading) {
    return (
      <div className="glassmorphism rounded-2xl p-6">
        <div className="flex items-center justify-center py-8">
          <div className="loading-spinner w-8 h-8 mr-3"></div>
          <span className="text-dark-300">Searching for "{query}"...</span>
        </div>
      </div>
    )
  }

  if (!query) {
    return (
      <div className="glassmorphism rounded-2xl p-6">
        <div className="text-center py-8">
          <div className="w-16 h-16 mx-auto mb-4 bg-dark-700 rounded-full flex items-center justify-center">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor" className="text-dark-400">
              <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
            </svg>
          </div>
          <p className="text-dark-300 mb-2">Start searching</p>
          <p className="text-sm text-dark-400">Enter a search term to find YouTube videos</p>
        </div>
      </div>
    )
  }

  if (results.length === 0 && !isLoading && !error) {
    return (
      <div className="glassmorphism rounded-2xl p-6">
        <div className="text-center py-8">
          <div className="w-16 h-16 mx-auto mb-4 bg-dark-700 rounded-full flex items-center justify-center">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor" className="text-dark-400">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"/>
            </svg>
          </div>
          <p className="text-dark-300 mb-2">No results found</p>
          <p className="text-sm text-dark-400">Try a different search term</p>
        </div>
      </div>
    )
  }

  return (
    <div className="glassmorphism rounded-2xl overflow-hidden">
      <div className="p-4 border-b border-white/10">
        <h2 className="text-lg font-semibold text-white">
          Search Results ({results.length}{paginationData ? ` of ${paginationData.totalResults.toLocaleString()}` : ''})
        </h2>
        <p className="text-sm text-dark-300">
          Results for "{query}"
        </p>
      </div>

      {/* Scrollable Results Container */}
      <div
        ref={scrollContainerRef}
        className="h-96 overflow-y-auto divide-y divide-white/10 search-results-scroll"
      >
        {results.map((result, index) => (
          <div key={`${result.id}-${index}`} className="search-result-item group">
            {/* Thumbnail */}
            <div className="relative flex-shrink-0">
              <Image
                src={result.thumbnail.url}
                alt={result.title}
                width={80}
                height={60}
                className="rounded-lg object-cover"
                style={{ width: 'auto', height: 'auto' }}
              />
              <span className="absolute bottom-1 right-1 bg-black/80 text-white text-xs px-1 rounded">
                {formatDuration(parseYouTubeDuration(result.duration))}
              </span>
            </div>

            {/* Content */}
            <div className="flex-1 min-w-0">
              <h4 className="font-medium text-white line-clamp-2 mb-1">
                {result.title}
              </h4>
              <p className="text-sm text-dark-300 mb-1">
                {result.channel.title}
              </p>
              {result.viewCount && (
                <p className="text-xs text-dark-400">
                  {formatNumber(result.viewCount)} views
                </p>
              )}
              <p className="text-xs text-dark-400 line-clamp-2 mt-1">
                {result.description}
              </p>
            </div>

            {/* Add to Queue/Draft Button */}
            <div className="flex-shrink-0">
              {(() => {
                const videoMetadata = youtubeService.searchResultToVideoMetadata(result)
                const hasInDraft = (isCreationMode || isEditMode) && isInDraft(videoMetadata.id)
                const isDraftMode = isCreationMode || isEditMode

                return (
                  <button
                    onClick={() => handleAddToQueue(result)}
                    className="px-3 py-2 text-sm opacity-0 group-hover:opacity-100 transition-opacity duration-200 btn-primary"
                  >
                    <>
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" className="mr-1">
                        <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                      </svg>
                      {isDraftMode ? (hasInDraft ? 'Add Another' : 'Add to Draft') : 'Add'}
                    </>
                  </button>
                )
              })()}
            </div>
          </div>
        ))}

        {/* Loading indicator at bottom when loading more */}
        {isLoading && results.length > 0 && (
          <div className="p-4 text-center border-t border-white/10">
            <div className="flex items-center justify-center space-x-2 text-dark-300">
              <div className="loading-spinner w-4 h-4"></div>
              <span className="text-sm">Loading more results...</span>
            </div>
          </div>
        )}

        {/* End of results indicator */}
        {paginationData && !paginationData.hasNextPage && results.length > 0 && (
          <div className="p-4 text-center border-t border-white/10">
            <p className="text-sm text-dark-400">
              End of results • {results.length} of {paginationData.totalResults.toLocaleString()} videos
            </p>
          </div>
        )}
      </div>
    </div>
  )
}
