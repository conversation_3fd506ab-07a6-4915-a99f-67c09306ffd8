'use client'

import { useEffect, useRef, useState } from 'react'
import { VideoTimeframe } from '@/lib/types/video'

interface InlineTimeframePreviewProps {
  videoId: string
  timeframe: VideoTimeframe
  onClose: () => void
}

declare global {
  interface Window {
    YT: any
    onYouTubeIframeAPIReady: () => void
  }
}

export function InlineTimeframePreview({ videoId, timeframe, onClose }: InlineTimeframePreviewProps) {
  const [playerElement, setPlayerElement] = useState<HTMLDivElement | null>(null)
  const [player, setPlayer] = useState<any>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)
  const monitorRef = useRef<NodeJS.Timeout | null>(null)
  const playerTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Callback ref to ensure we get notified when the element is attached
  const playerRefCallback = (element: HTMLDivElement | null) => {
    console.log('🎬 [INLINE PREVIEW] Ref callback triggered:', !!element)
    setPlayerElement(element)
  }

  // Initialize YouTube API if not already loaded
  useEffect(() => {
    if (typeof window !== 'undefined' && !window.YT) {
      console.log('🎬 [INLINE PREVIEW] Loading YouTube API...')
      const tag = document.createElement('script')
      tag.src = 'https://www.youtube.com/iframe_api'
      const firstScriptTag = document.getElementsByTagName('script')[0]
      firstScriptTag.parentNode?.insertBefore(tag, firstScriptTag)
      
      // Set up global callback for when API is ready
      window.onYouTubeIframeAPIReady = () => {
        console.log('🎬 [INLINE PREVIEW] YouTube API loaded')
      }
    } else if (window.YT) {
      console.log('🎬 [INLINE PREVIEW] YouTube API already available')
    }
  }, [])

  // Create player when element is available
  useEffect(() => {
    console.log('🎬 [INLINE PREVIEW] useEffect triggered', {
      hasPlayerElement: !!playerElement,
      hasPlayer: !!player,
      hasYT: !!window.YT,
      hasYTPlayer: !!(window.YT && window.YT.Player)
    })
    
    if (!playerElement || player) {
      return
    }
    
    console.log('🎬 [INLINE PREVIEW] All conditions met, creating player...')
    
    if (window.YT && window.YT.Player) {
      setIsLoading(true)
      console.log('🎬 [INLINE PREVIEW] Creating YouTube player...')
      
      // Create a unique ID for this player instance
      const playerId = `inline-preview-player-${Date.now()}`
      playerElement.id = playerId
      
      try {
        const newPlayer = new window.YT.Player(playerId, {
          height: '120',
          width: '200',
          videoId: videoId,
          playerVars: {
            start: Math.floor(timeframe.startTime),
            autoplay: 0,
            controls: 1,
            modestbranding: 1,
            rel: 0,
            showinfo: 0,
            iv_load_policy: 3, // Hide annotations
            origin: window.location.origin, // Add origin for CORS
          },
          events: {
            onReady: (event: any) => {
              console.log('🎬 [INLINE PREVIEW] Player ready!', event.target)
              // Clear the timeout since player is ready
              if (playerTimeoutRef.current) {
                clearTimeout(playerTimeoutRef.current)
                playerTimeoutRef.current = null
              }
              setIsLoading(false)
              setPlayer(event.target)
            },
            onStateChange: (event: any) => {
              const state = event.data
              console.log('🎬 [INLINE PREVIEW] State change:', state)
              if (state === 1) { // Playing
                setIsPlaying(true)
                startTimeframeMonitoring(event.target)
              } else if (state === 2) { // Paused
                setIsPlaying(false)
                stopTimeframeMonitoring()
              } else if (state === 0) { // Ended
                setIsPlaying(false)
                stopTimeframeMonitoring()
                // Auto-restart the timeframe
                event.target.seekTo(timeframe.startTime, true)
              }
            },
            onError: (event: any) => {
              console.error('❌ [INLINE PREVIEW] Player error:', event.data)
              setIsLoading(false)
              setHasError(true)
            }
          }
        })
        
        // Set a timeout to detect if player creation hangs
        playerTimeoutRef.current = setTimeout(() => {
          console.error('❌ [INLINE PREVIEW] Player creation timeout - onReady never fired')
          setIsLoading(false)
          setHasError(true)
        }, 10000) // 10 second timeout
        
      } catch (error) {
        console.error('❌ [INLINE PREVIEW] Failed to create player:', error)
        setIsLoading(false)
        setHasError(true)
      }
    } else {
      // YouTube API not ready yet, wait a bit and try again
      console.log('🎬 [INLINE PREVIEW] YouTube API not ready, waiting...')
      setIsLoading(true)
      
      let attempts = 0
      const maxAttempts = 10
      
      const checkAPI = () => {
        attempts++
        console.log(`🎬 [INLINE PREVIEW] Checking API attempt ${attempts}/${maxAttempts}`)
        
        if (window.YT && window.YT.Player) {
          console.log('🎬 [INLINE PREVIEW] API now ready, triggering re-render')
          setIsLoading(false)
          // Force re-render by updating a state
          setIsLoading(true)
        } else if (attempts < maxAttempts) {
          setTimeout(checkAPI, 1000)
        } else {
          console.error('🎬 [INLINE PREVIEW] Failed to load YouTube API after', maxAttempts, 'attempts')
          setIsLoading(false)
          setHasError(true)
        }
      }

      const timeout = setTimeout(checkAPI, 1000)
      return () => clearTimeout(timeout)
    }
  }, [videoId, timeframe.startTime, timeframe.endTime, playerElement, player])

  // Monitor timeframe boundaries
  const startTimeframeMonitoring = (playerInstance: any) => {
    if (monitorRef.current) {
      clearInterval(monitorRef.current)
    }

    monitorRef.current = setInterval(() => {
      try {
        if (playerInstance && typeof playerInstance.getCurrentTime === 'function') {
          const currentTime = playerInstance.getCurrentTime()
          
          if (typeof currentTime === 'number' && !isNaN(currentTime)) {
            // Check if we've reached the end of the timeframe
            if (currentTime >= timeframe.endTime - 0.5) {
              console.log(`🎬 [INLINE PREVIEW] Reached timeframe end at ${currentTime}s`)
              stopTimeframeMonitoring()
              // Loop back to start of timeframe
              playerInstance.seekTo(timeframe.startTime, true)
            }
          }
        }
      } catch (error) {
        console.error('❌ Error monitoring inline preview timeframe:', error)
      }
    }, 500)
  }

  const stopTimeframeMonitoring = () => {
    if (monitorRef.current) {
      clearInterval(monitorRef.current)
      monitorRef.current = null
    }
  }

  // Cleanup when component unmounts
  useEffect(() => {
    return () => {
      stopTimeframeMonitoring()
      if (playerTimeoutRef.current) {
        clearTimeout(playerTimeoutRef.current)
        playerTimeoutRef.current = null
      }
      if (player) {
        try {
          player.destroy()
        } catch (error) {
          console.error('Error destroying inline preview player:', error)
        }
      }
    }
  }, [player])

  const handlePlayPause = () => {
    if (player) {
      if (isPlaying) {
        player.pauseVideo()
      } else {
        // Ensure we're at the right position before playing
        player.seekTo(timeframe.startTime, true)
        player.playVideo()
      }
    }
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  return (
    <div className="bg-dark-600/30 rounded p-3 mt-2 border border-dark-500">
      <div className="flex items-center justify-between mb-2">
        <div className="text-xs text-dark-300">
          Preview: {formatTime(timeframe.startTime)} - {formatTime(timeframe.endTime)}
        </div>
        <button
          onClick={onClose}
          className="text-dark-400 hover:text-white p-1"
          title="Close preview"
        >
          <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
            <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
          </svg>
        </button>
      </div>

      <div className="flex gap-3">
        <div className="flex-shrink-0">
          {hasError ? (
            <div className="bg-dark-700 rounded flex flex-col items-center justify-center h-[120px] w-[200px]">
              <div className="text-red-400 text-xs mb-1">Failed to load</div>
              <button
                onClick={() => {
                  setHasError(false)
                  setIsLoading(true)
                  setPlayer(null)
                }}
                className="text-xs text-primary-400 hover:text-primary-300"
              >
                Retry
              </button>
            </div>
          ) : (
            <div className="relative">
              <div ref={playerRefCallback} className="rounded overflow-hidden" />
              {!player && (
                <div className="absolute inset-0 bg-dark-700 rounded flex flex-col items-center justify-center h-[120px] w-[200px]">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-500 mb-1"></div>
                  <div className="text-dark-400 text-xs text-center">
                    Loading...
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        <div className="flex-1 flex flex-col justify-center">
          <div className="flex gap-2 mb-2">
            <button
              onClick={handlePlayPause}
              disabled={isLoading || hasError}
              className="btn-primary p-2 flex items-center justify-center"
              title={isPlaying ? "Pause" : "Play"}
            >
              {isPlaying ? (
                <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/>
                </svg>
              ) : (
                <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M8 5v14l11-7z"/>
                </svg>
              )}
            </button>
          </div>
          
          <div className="text-xs text-dark-400">
            Loops {timeframe.loopCount} time{timeframe.loopCount > 1 ? 's' : ''}
          </div>
        </div>
      </div>
    </div>
  )
}
