'use client'

import { useState } from 'react'
import { useDraftQueue } from '@/hooks/useDraftQueue'
import { formatDuration } from '@/lib/utils/format'
import { formatTime } from '@/lib/utils/time'
import { DraftVideoItem, VideoTimeframe } from '@/lib/types/video'
import { InlineTimeframePreview } from './InlineTimeframePreview'

// Component for managing timeframes
function TimeframeManager({ item }: { item: DraftVideoItem }) {
  const { addTimeframe, removeTimeframe, updateTimeframe } = useDraftQueue()
  const [isAddingTimeframe, setIsAddingTimeframe] = useState(false)
  const [editingTimeframeId, setEditingTimeframeId] = useState<string | null>(null)
  const [previewingTimeframeId, setPreviewingTimeframeId] = useState<string | null>(null)
  const [newStartTime, setNewStartTime] = useState(0)
  const [newEndTime, setNewEndTime] = useState(Math.min(30, item.duration))
  const [newLoopCount, setNewLoopCount] = useState(1)

  const handleAddTimeframe = () => {
    if (newEndTime > newStartTime && newStartTime >= 0 && newEndTime <= item.duration) {
      const timeframeId = addTimeframe(item.draftId, newStartTime, newEndTime, newLoopCount)
      if (timeframeId) {
        setIsAddingTimeframe(false)
        setNewStartTime(0)
        setNewEndTime(Math.min(30, item.duration))
        setNewLoopCount(1)
      }
    }
  }

  const isValidTimeframe = newEndTime > newStartTime && newStartTime >= 0 && newEndTime <= item.duration

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && isValidTimeframe) {
      handleAddTimeframe()
    } else if (e.key === 'Escape') {
      setIsAddingTimeframe(false)
    }
  }

  const handleUpdateTimeframe = (timeframeId: string, updates: Partial<VideoTimeframe>) => {
    updateTimeframe(item.draftId, timeframeId, updates)
  }

  const handleRemoveTimeframe = (timeframeId: string) => {
    removeTimeframe(item.draftId, timeframeId)
  }

  return (
    <div className="mt-2 space-y-2">
      <div className="flex items-center justify-between">
        <span className="text-xs text-dark-300">Timeframes ({item.timeframes.length})</span>
        <button
          onClick={() => setIsAddingTimeframe(!isAddingTimeframe)}
          className="text-xs text-primary-400 hover:text-primary-300 flex items-center gap-1"
        >
          <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
            <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
          </svg>
          Add
        </button>
      </div>

      {/* Existing timeframes */}
      {item.timeframes.map((timeframe, index) => (
        <div key={timeframe.id} className="bg-dark-600/50 rounded p-2 text-xs">
          <div className="flex items-center justify-between mb-1">
            <div className="flex items-center gap-2">
              <span className="bg-primary-600/20 text-primary-300 px-1.5 py-0.5 rounded text-xs font-mono">
                #{index + 1}
              </span>
              {editingTimeframeId === timeframe.id ? (
                <div className="flex items-center gap-1">
                  <input
                    type="number"
                    min="0"
                    max={item.duration}
                    step="1"
                    value={timeframe.startTime}
                    onChange={(e) => handleUpdateTimeframe(timeframe.id, { startTime: Math.max(0, Math.min(item.duration, parseInt(e.target.value) || 0)) })}
                    className="input-field text-xs py-0.5 px-1 w-12"
                  />
                  <span className="text-dark-400">-</span>
                  <input
                    type="number"
                    min={timeframe.startTime + 1}
                    max={item.duration}
                    step="1"
                    value={timeframe.endTime}
                    onChange={(e) => handleUpdateTimeframe(timeframe.id, { endTime: Math.max(timeframe.startTime + 1, Math.min(item.duration, parseInt(e.target.value) || item.duration)) })}
                    className="input-field text-xs py-0.5 px-1 w-12"
                  />
                </div>
              ) : (
                <span className="text-dark-200 cursor-pointer hover:text-white" onClick={() => setEditingTimeframeId(timeframe.id)}>
                  {formatTime(timeframe.startTime)} - {formatTime(timeframe.endTime)}
                </span>
              )}
            </div>
            <div className="flex items-center gap-1">
              {editingTimeframeId === timeframe.id ? (
                <button
                  onClick={() => setEditingTimeframeId(null)}
                  className="text-green-400 hover:text-green-300 p-1"
                  title="Done editing"
                >
                  <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                  </svg>
                </button>
              ) : (
                <>
                  <button
                    onClick={() => setPreviewingTimeframeId(previewingTimeframeId === timeframe.id ? null : timeframe.id)}
                    className={`p-1 ${previewingTimeframeId === timeframe.id ? 'text-primary-300' : 'text-primary-400 hover:text-primary-300'}`}
                    title={previewingTimeframeId === timeframe.id ? "Hide preview" : "Show preview"}
                  >
                    <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M8 5v14l11-7z"/>
                    </svg>
                  </button>
                  <button
                    onClick={() => setEditingTimeframeId(timeframe.id)}
                    className="text-blue-400 hover:text-blue-300 p-1"
                    title="Edit timeframe"
                  >
                    <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
                    </svg>
                  </button>
                </>
              )}
              <button
                onClick={() => handleRemoveTimeframe(timeframe.id)}
                className="text-red-400 hover:text-red-300 p-1"
                title="Remove timeframe"
              >
                <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                </svg>
              </button>
            </div>
          </div>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <span className="text-dark-400">Loop:</span>
              <input
                type="number"
                min="1"
                max="99"
                value={timeframe.loopCount}
                onChange={(e) => handleUpdateTimeframe(timeframe.id, { loopCount: Math.max(1, parseInt(e.target.value) || 1) })}
                className="input-field text-xs py-0.5 px-1 w-12"
              />
            </div>
            <div className="text-xs text-dark-400">
              {formatTime(timeframe.endTime - timeframe.startTime)} duration
            </div>
          </div>

          {/* Inline Preview */}
          {previewingTimeframeId === timeframe.id && (
            <InlineTimeframePreview
              videoId={item.id}
              timeframe={timeframe}
              onClose={() => setPreviewingTimeframeId(null)}
            />
          )}
        </div>
      ))}

      {/* Add new timeframe form */}
      {isAddingTimeframe && (
        <div className="bg-dark-600/50 rounded p-2 space-y-2">
          <div className="grid grid-cols-2 gap-2">
            <div>
              <label className="text-xs text-dark-300 block mb-1">Start (s)</label>
              <input
                type="number"
                min="0"
                max={item.duration}
                step="1"
                value={newStartTime}
                onChange={(e) => setNewStartTime(Math.max(0, Math.min(item.duration, parseInt(e.target.value) || 0)))}
                onKeyDown={handleKeyDown}
                className="input-field text-xs py-1 px-2 w-full"
                autoFocus
              />
            </div>
            <div>
              <label className="text-xs text-dark-300 block mb-1">End (s)</label>
              <input
                type="number"
                min={newStartTime + 1}
                max={item.duration}
                step="1"
                value={newEndTime}
                onChange={(e) => setNewEndTime(Math.max(newStartTime + 1, Math.min(item.duration, parseInt(e.target.value) || item.duration)))}
                onKeyDown={handleKeyDown}
                className="input-field text-xs py-1 px-2 w-full"
              />
            </div>
          </div>
          <div>
            <label className="text-xs text-dark-300 block mb-1">Loop Count</label>
            <input
              type="number"
              min="1"
              max="99"
              value={newLoopCount}
              onChange={(e) => setNewLoopCount(Math.max(1, parseInt(e.target.value) || 1))}
              onKeyDown={handleKeyDown}
              className="input-field text-xs py-1 px-2 w-16"
            />
          </div>
          <div className="flex gap-1">
            <button
              onClick={handleAddTimeframe}
              disabled={!isValidTimeframe}
              className={`text-xs px-2 py-1 flex-1 ${
                isValidTimeframe
                  ? 'btn-primary'
                  : 'bg-dark-600 text-dark-400 cursor-not-allowed'
              }`}
            >
              Add Timeframe
            </button>
            <button
              onClick={() => setIsAddingTimeframe(false)}
              className="btn-secondary text-xs px-2 py-1 flex-1"
            >
              Cancel
            </button>
          </div>
          {!isValidTimeframe && (
            <div className="text-xs text-red-400 mt-1">
              End time must be greater than start time and within video duration
            </div>
          )}
          <div className="text-xs text-dark-400 mt-1">
            Press Enter to add, Escape to cancel
          </div>
        </div>
      )}


    </div>
  )
}

// Component for editing individual draft item properties
function DraftItemEditor({ item, onUpdate, onRemove }: {
  item: DraftVideoItem
  onUpdate: (updates: Partial<Pick<DraftVideoItem, 'timeframes' | 'loopSettings'>>) => void
  onRemove: () => void
}) {
  const [isEditing, setIsEditing] = useState(false)
  const [videoLoopCount, setVideoLoopCount] = useState(item.loopSettings.videoLoopCount)
  const [loopMode, setLoopMode] = useState(item.loopSettings.loopMode)

  const handleSave = () => {
    onUpdate({
      loopSettings: {
        videoLoopCount,
        loopMode
      }
    })
    setIsEditing(false)
  }

  const handleCancel = () => {
    setVideoLoopCount(item.loopSettings.videoLoopCount)
    setLoopMode(item.loopSettings.loopMode)
    setIsEditing(false)
  }



  return (
    <div className="flex items-center gap-3 p-3 bg-dark-800/30 border border-dark-600/30 rounded-lg hover:bg-dark-700/30 transition-colors">
      {/* Thumbnail */}
      <div className="relative flex-shrink-0">
        <img
          src={item.thumbnail}
          alt={item.title}
          className="w-16 h-12 rounded-lg object-cover"
          loading="lazy"
        />
        <div className="absolute -top-1 -left-1 w-5 h-5 bg-primary-500 rounded-full flex items-center justify-center text-white text-xs font-bold">
          {item.loopSettings.videoLoopCount}
        </div>
      </div>

      {/* Video Info */}
      <div className="flex-1 min-w-0">
        <div className="text-white font-medium text-sm line-clamp-2 mb-1">
          {item.title}
        </div>
        <div className="text-dark-300 text-xs mb-1">
          {formatDuration(item.duration)} • {item.channel || 'YouTube'}
        </div>
        {item.timeframes.length > 0 && (
          <div className="text-primary-400 text-xs">
            {item.timeframes.length} timeframe{item.timeframes.length > 1 ? 's' : ''} • {item.loopSettings.loopMode === 'timeframes-only' ? 'Timeframes only' : 'Full video + timeframes'}
          </div>
        )}
      </div>

      {/* Controls */}
      <div className="flex items-center space-x-2">
        {!isEditing ? (
          <>
            <button
              onClick={() => setIsEditing(true)}
              className="p-2 text-dark-300 hover:text-primary-400 hover:bg-primary-400/10 rounded-lg transition-colors"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
              </svg>
            </button>
            <button
              onClick={onRemove}
              className="p-2 text-dark-300 hover:text-red-400 hover:bg-red-400/10 rounded-lg transition-colors"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
              </svg>
            </button>
          </>
        ) : (
          <div className="flex flex-col gap-2 p-2 bg-dark-700/50 rounded-lg min-w-48">
            {/* Video Loop Count */}
            <div className="flex items-center gap-2">
              <label className="text-xs text-dark-300 w-16">Video Loop:</label>
              <input
                type="number"
                min="1"
                max="99"
                value={videoLoopCount}
                onChange={(e) => setVideoLoopCount(Math.max(1, parseInt(e.target.value) || 1))}
                className="input-field text-xs py-1 px-2 w-16"
              />
            </div>

            {/* Loop Mode */}
            <div className="flex items-center gap-2">
              <label className="text-xs text-dark-300 w-16">Mode:</label>
              <select
                value={loopMode}
                onChange={(e) => setLoopMode(e.target.value as 'timeframes-only' | 'whole-video-plus-timeframes')}
                className="input-field text-xs py-1 px-2 flex-1"
              >
                <option value="whole-video-plus-timeframes">Full + Timeframes</option>
                <option value="timeframes-only">Timeframes Only</option>
              </select>
            </div>

            {/* Timeframes Management */}
            <TimeframeManager item={item} />

            {/* Action Buttons */}
            <div className="flex gap-1 mt-1">
              <button
                onClick={handleSave}
                className="btn-primary text-xs px-2 py-1 flex-1"
              >
                Save
              </button>
              <button
                onClick={handleCancel}
                className="btn-secondary text-xs px-2 py-1 flex-1"
              >
                Cancel
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export function DraftQueue() {
  const { draftItems, draftCount, removeFromDraft, updateDraftItem, clearDraft } = useDraftQueue()

  if (draftCount === 0) {
    return (
      <div className="glassmorphism rounded-2xl p-6">
        <div className="flex items-center justify-between mb-4">
          <h4 className="text-lg font-semibold text-white">
            Draft Queue <span className="text-primary-400">({draftCount})</span>
          </h4>
        </div>
        <div className="text-center py-8">
          <div className="text-4xl mb-4">🎵</div>
          <p className="text-dark-300 mb-2">No videos in draft queue</p>
          <p className="text-dark-400 text-sm">Search and add videos to build your queue!</p>
        </div>
      </div>
    )
  }

  return (
    <div className="glassmorphism rounded-2xl p-6">
      <div className="flex items-center justify-between mb-4">
        <h4 className="text-lg font-semibold text-white">
          Draft Queue <span className="text-primary-400">({draftCount})</span>
        </h4>
        <button
          onClick={clearDraft}
          className="btn-secondary text-sm px-3 py-1"
          title="Clear draft queue"
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" className="mr-1">
            <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
          </svg>
          Clear
        </button>
      </div>

      <div className="space-y-3 max-h-80 overflow-y-auto">
        {draftItems.map((draftItem) => (
          <DraftItemEditor
            key={draftItem.draftId}
            item={draftItem}
            onUpdate={(updates) => updateDraftItem(draftItem.draftId, updates)}
            onRemove={() => removeFromDraft(draftItem.draftId)}
          />
        ))}
      </div>
    </div>
  )
}
