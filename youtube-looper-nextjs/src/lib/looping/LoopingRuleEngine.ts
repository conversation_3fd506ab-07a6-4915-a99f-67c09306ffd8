/**
 * Looping Rule Engine
 * 
 * This engine implements the two main rules:
 * RULE 1: Queue counter should always be respected (infinite or specific number)
 * RULE 2: Each video has mode: TF only or whole video
 *   - TF only: loops only timeframes with individual counters
 *   - Whole video: starts from 0, then respects timeframe loops
 */

export interface TimeframeData {
  id: string
  startTime: number
  endTime: number
  loopCount: number
}

export interface VideoLoopSettings {
  videoLoopCount: number
  loopMode: 'timeframes-only' | 'whole-video-plus-timeframes'
}

export interface LoopingState {
  videoId: string
  videoLoopCount: number
  currentTimeframeIndex: number
  timeframeLoopCounts: { [timeframeId: string]: number }
  queueLoopCount: number // -1 for infinite
}

export interface PlaybackSegment {
  type: 'timeframe-loop' | 'next-timeframe' | 'video-loop' | 'start-timeframes' | 'continue-video' | 'video-complete' | 'queue-complete'
  seekTo?: number
  timeframeIndex?: number
  message?: string
}

export class LoopingRuleEngine {
  /**
   * RULE 1: Check if queue should continue looping
   */
  private shouldQueueLoop(queueLoopCount: number): boolean {
    return queueLoopCount === -1 || queueLoopCount > 0
  }

  /**
   * RULE 2: Determine video start position based on mode
   */
  getVideoStartPosition(timeframes: TimeframeData[], loopSettings: VideoLoopSettings): number {
    if (loopSettings.loopMode === 'timeframes-only' && timeframes.length > 0) {
      return timeframes[0].startTime
    }
    return 0 // Start from beginning for whole-video mode
  }

  /**
   * RULE 2: Determine if we should start timeframe monitoring immediately
   */
  shouldStartTimeframeMonitoring(timeframes: TimeframeData[], loopSettings: VideoLoopSettings, hasStartedTimeframes: boolean): boolean {
    if (timeframes.length === 0) return false

    if (loopSettings.loopMode === 'timeframes-only') {
      return true // Always monitor in timeframes-only mode
    }

    // In whole-video-plus-timeframes mode, always monitor to detect when we enter timeframes
    return true
  }

  /**
   * Main decision engine: What should happen when a timeframe ends?
   */
  getNextPlaybackSegment(
    timeframes: TimeframeData[],
    loopSettings: VideoLoopSettings,
    state: LoopingState
  ): PlaybackSegment {
    // No timeframes = simple video looping
    if (timeframes.length === 0) {
      return this.handleNoTimeframes(loopSettings, state)
    }

    // Handle based on video mode
    if (loopSettings.loopMode === 'timeframes-only') {
      return this.handleTimeframesOnlyMode(timeframes, loopSettings, state)
    } else {
      return this.handleWholeVideoPlusTimeframesMode(timeframes, loopSettings, state)
    }
  }

  /**
   * Handle videos with no timeframes
   */
  private handleNoTimeframes(loopSettings: VideoLoopSettings, state: LoopingState): PlaybackSegment {
    console.log(`🔍 [DEBUG] handleNoTimeframes - state.videoLoopCount: ${state.videoLoopCount}, loopSettings.videoLoopCount: ${loopSettings.videoLoopCount}`)

    if (state.videoLoopCount + 1 < loopSettings.videoLoopCount) {
      console.log(`🔍 [DEBUG] Video should loop: ${state.videoLoopCount + 1} < ${loopSettings.videoLoopCount}`)
      return {
        type: 'video-loop',
        seekTo: 0,
        message: `Looping whole video (${state.videoLoopCount + 1}/${loopSettings.videoLoopCount})`
      }
    }

    console.log(`🔍 [DEBUG] Video should complete: ${state.videoLoopCount + 1} >= ${loopSettings.videoLoopCount}`)
    return {
      type: 'video-complete',
      message: 'Video completed all loops'
    }
  }

  /**
   * RULE 2: Handle timeframes-only mode
   * Logic: Loop each timeframe individually, then loop the whole sequence
   */
  private handleTimeframesOnlyMode(
    timeframes: TimeframeData[],
    loopSettings: VideoLoopSettings,
    state: LoopingState
  ): PlaybackSegment {
    const currentTimeframe = timeframes[state.currentTimeframeIndex]
    if (!currentTimeframe) {
      return { type: 'video-complete', message: 'No current timeframe found' }
    }

    const timeframeLoops = state.timeframeLoopCounts[currentTimeframe.id] || 0

    // Check if current timeframe needs more loops
    if (timeframeLoops + 1 < currentTimeframe.loopCount) {
      return {
        type: 'timeframe-loop',
        seekTo: currentTimeframe.startTime,
        timeframeIndex: state.currentTimeframeIndex,
        message: `Looping timeframe ${state.currentTimeframeIndex + 1} (${timeframeLoops + 1}/${currentTimeframe.loopCount})`
      }
    }

    // Current timeframe is done, move to next
    const nextTimeframeIndex = state.currentTimeframeIndex + 1
    if (nextTimeframeIndex < timeframes.length) {
      return {
        type: 'next-timeframe',
        seekTo: timeframes[nextTimeframeIndex].startTime,
        timeframeIndex: nextTimeframeIndex,
        message: `Moving to timeframe ${nextTimeframeIndex + 1}`
      }
    }

    // All timeframes done, check video loop
    if (state.videoLoopCount + 1 < loopSettings.videoLoopCount) {
      return {
        type: 'video-loop',
        seekTo: timeframes[0].startTime,
        timeframeIndex: 0,
        message: `Video loop ${state.videoLoopCount + 1}/${loopSettings.videoLoopCount} - restarting timeframes`
      }
    }

    return {
      type: 'video-complete',
      message: 'All timeframes and video loops completed'
    }
  }

  /**
   * RULE 2: Handle whole-video-plus-timeframes mode
   * Logic: Video plays normally, but when it hits timeframe sections, those sections loop
   */
  private handleWholeVideoPlusTimeframesMode(
    timeframes: TimeframeData[],
    loopSettings: VideoLoopSettings,
    state: LoopingState
  ): PlaybackSegment {
    // This method should only be called when a timeframe ends (not when video ends naturally)
    // If we're here, we're processing a timeframe that just ended

    const currentTimeframe = timeframes[state.currentTimeframeIndex]
    if (!currentTimeframe) {
      return { type: 'video-complete', message: 'No current timeframe found' }
    }

    const timeframeLoops = state.timeframeLoopCounts[currentTimeframe.id] || 0

    // Check if current timeframe needs more loops
    if (timeframeLoops + 1 < currentTimeframe.loopCount) {
      return {
        type: 'timeframe-loop',
        seekTo: currentTimeframe.startTime,
        timeframeIndex: state.currentTimeframeIndex,
        message: `Looping timeframe ${state.currentTimeframeIndex + 1} (${timeframeLoops + 1}/${currentTimeframe.loopCount})`
      }
    }

    // Current timeframe is done, continue video from where timeframe ends
    return {
      type: 'continue-video',
      seekTo: currentTimeframe.endTime,
      message: `Timeframe ${state.currentTimeframeIndex + 1} completed, continuing video from ${currentTimeframe.endTime}s`
    }
  }

  /**
   * RULE 1: Handle queue progression
   */
  shouldMoveToNextVideo(queueLoopCount: number): boolean {
    return this.shouldQueueLoop(queueLoopCount)
  }

  /**
   * RULE 1: Handle queue completion
   */
  getQueueAction(currentIndex: number, totalVideos: number, queueLoopCount: number): 'next-video' | 'loop-queue' | 'queue-complete' {
    const nextIndex = currentIndex + 1
    
    if (nextIndex < totalVideos) {
      return 'next-video'
    }
    
    if (this.shouldQueueLoop(queueLoopCount)) {
      return 'loop-queue'
    }
    
    return 'queue-complete'
  }

  /**
   * Helper: Create initial state for a video
   */
  createInitialState(videoId: string, timeframes: TimeframeData[], queueLoopCount: number): LoopingState {
    // Only pre-populate timeframeLoopCounts for timeframes-only mode
    // For whole-video-plus-timeframes mode, we'll add them when timeframes actually start
    const timeframeLoopCounts: { [timeframeId: string]: number } = {}

    return {
      videoId,
      videoLoopCount: 0,
      currentTimeframeIndex: 0,
      timeframeLoopCounts,
      queueLoopCount
    }
  }

  /**
   * Helper: Update state after a playback segment
   */
  updateStateAfterSegment(state: LoopingState, segment: PlaybackSegment, currentTimeframeId?: string): LoopingState {
    const newState = { ...state }

    switch (segment.type) {
      case 'timeframe-loop':
        if (currentTimeframeId) {
          newState.timeframeLoopCounts = {
            ...newState.timeframeLoopCounts,
            [currentTimeframeId]: (newState.timeframeLoopCounts[currentTimeframeId] || 0) + 1
          }
        }
        break

      case 'next-timeframe':
        if (segment.timeframeIndex !== undefined) {
          newState.currentTimeframeIndex = segment.timeframeIndex
        }
        break

      case 'video-loop':
        newState.videoLoopCount += 1
        newState.currentTimeframeIndex = 0
        // Reset timeframe counters - clear them completely for whole-video-plus-timeframes mode
        // so that timeframes will be reinitialized when we reach them again
        newState.timeframeLoopCounts = {}
        break

      case 'start-timeframes':
        newState.currentTimeframeIndex = 0
        break

      case 'continue-video':
        // Timeframe is done, video continues normally
        // No state changes needed - video will play until next timeframe or end
        break
    }

    return newState
  }
}

// Export singleton instance
export const loopingRuleEngine = new LoopingRuleEngine()
