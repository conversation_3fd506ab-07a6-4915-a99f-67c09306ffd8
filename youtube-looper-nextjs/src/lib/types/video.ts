// Video-related types and interfaces

export interface VideoMetadata {
  id: string
  title: string
  thumbnail: string
  duration: number
  channel?: string
  description?: string
  publishedAt?: string
  viewCount?: number
  url: string
}

// Video timeframe with individual loop counter
export interface VideoTimeframe {
  id: string // unique identifier for each timeframe
  startTime: number
  endTime: number
  loopCount: number // how many times this specific timeframe should loop
}

// Video loop settings
export interface VideoLoopSettings {
  videoLoopCount: number // how many times the entire video (with its timeframes) should loop
  loopMode: 'timeframes-only' | 'whole-video-plus-timeframes' // what to loop
}

// Enhanced video item for draft queue with multiple timeframes support
export interface DraftVideoItem extends VideoMetadata {
  draftId: string // Unique identifier for this draft item (allows same video multiple times)
  timeframes: VideoTimeframe[] // Multiple custom timeframes with individual loop counters
  loopSettings: VideoLoopSettings // Video-level loop settings
  addedToDraftAt: number // Timestamp when added to draft
}

export interface VideoSearchResult {
  id: string
  title: string
  description: string
  thumbnail: {
    url: string
    width: number
    height: number
  }
  channel: {
    title: string
    id: string
  }
  duration: string
  publishedAt: string
  viewCount?: number
}

export interface YouTubePlayerState {
  UNSTARTED: -1
  ENDED: 0
  PLAYING: 1
  PAUSED: 2
  BUFFERING: 3
  CUED: 5
}

export interface YouTubePlayer {
  playVideo(): void
  pauseVideo(): void
  stopVideo(): void
  seekTo(seconds: number, allowSeekAhead?: boolean): void
  getCurrentTime(): number
  getDuration(): number
  getPlayerState(): number
  getVideoData(): {
    video_id: string
    title: string
    author: string
  }
  loadVideoById(videoId: string | { videoId: string; startSeconds?: number; endSeconds?: number }): void
  destroy(): void
}

export interface YouTubePlayerEvent {
  target: YouTubePlayer
  data?: number
}

// YouTube API response types
export interface YouTubeSearchResponse {
  kind: string
  etag: string
  nextPageToken?: string
  prevPageToken?: string
  regionCode: string
  pageInfo: {
    totalResults: number
    resultsPerPage: number
  }
  items: YouTubeVideoItem[]
}

export interface YouTubeVideoItem {
  kind: string
  etag: string
  id: {
    kind: string
    videoId: string
  }
  snippet: {
    publishedAt: string
    channelId: string
    title: string
    description: string
    thumbnails: {
      default: YouTubeThumbnail
      medium: YouTubeThumbnail
      high: YouTubeThumbnail
      standard?: YouTubeThumbnail
      maxres?: YouTubeThumbnail
    }
    channelTitle: string
    liveBroadcastContent: string
    publishTime: string
  }
}

export interface YouTubeThumbnail {
  url: string
  width: number
  height: number
}

export interface YouTubeVideoDetailsResponse {
  kind: string
  etag: string
  items: YouTubeVideoDetails[]
}

export interface YouTubeVideoDetails {
  kind: string
  etag: string
  id: string
  snippet: {
    publishedAt: string
    channelId: string
    title: string
    description: string
    thumbnails: {
      default: YouTubeThumbnail
      medium: YouTubeThumbnail
      high: YouTubeThumbnail
      standard?: YouTubeThumbnail
      maxres?: YouTubeThumbnail
    }
    channelTitle: string
    categoryId: string
    liveBroadcastContent: string
    defaultLanguage?: string
    defaultAudioLanguage?: string
  }
  contentDetails: {
    duration: string
    dimension: string
    definition: string
    caption: string
    licensedContent: boolean
    regionRestriction?: {
      allowed?: string[]
      blocked?: string[]
    }
    contentRating: object
    projection: string
  }
  statistics: {
    viewCount: string
    likeCount?: string
    dislikeCount?: string
    favoriteCount: string
    commentCount?: string
  }
}
