import { QueueState } from '@/lib/types/queue'

/**
 * Transform queue data from old Hugo app format to new React app format
 * Handles the migration from 'queue' field to 'items' field and ensures
 * all video items have the correct structure for QueueItem interface
 */
export function transformQueueData(queueData: any): QueueState {
  // Get the video items from either 'items' or 'queue' field
  const rawItems = queueData.items || queueData.queue || []
  
  // Transform video items to match QueueItem interface
  const transformedItems = rawItems.map((item: any, index: number) => ({
    // VideoMetadata fields
    id: item.id,
    title: item.title,
    thumbnail: item.thumbnail,
    duration: item.duration || 0,
    channel: item.channel,
    description: item.description,
    publishedAt: item.publishedAt,
    viewCount: item.viewCount,
    url: item.url || `https://www.youtube.com/watch?v=${item.id}`,
    // QueueItem specific fields
    addedAt: item.addedAt || Date.now(),
    queueIndex: index
  }))
  
  // Transform queue data structure
  return {
    items: transformedItems,
    currentIndex: queueData.currentIndex || 0,
    isPlaying: false, // Don't auto-play loaded queues
    queueLoopCount: (queueData as any).queueLoopCount !== undefined ? (queueData as any).queueLoopCount : -1,
    shuffle: queueData.shuffle || false,
    volume: queueData.volume || 1,
    timestamp: Date.now()
  }
}
