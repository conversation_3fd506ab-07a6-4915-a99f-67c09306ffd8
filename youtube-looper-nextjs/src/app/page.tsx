'use client'

import { MainLayout } from '@/components/layout/MainLayout'
import { VideoPlayer } from '@/components/video-player/VideoPlayer'
import { TimeframeDisplay } from '@/components/video-player/TimeframeDisplay'
import { CurrentQueue } from '@/components/queue/CurrentQueue'
import { SearchView } from '@/components/search/SearchView'
import { PersonalQueuesView } from '@/components/personal-queues/PersonalQueuesView'
import { PublicQueuesView } from '@/components/public-queues/PublicQueuesView'
import { useNavigation } from '@/hooks/useNavigation'
import { useHeaderMinimize } from '@/components/layout/Header'
import { useQueue } from '@/hooks/useQueue'
import { firebaseService } from '@/lib/services/firebase'
import { useEffect } from 'react'

export default function HomePage() {
  const { activeView } = useNavigation()
  const { isMinimized } = useHeaderMinimize()
  const { loadQueue } = useQueue()

  useEffect(() => {
    // Initialize YouTube API when component mounts
    const initYouTubeAPI = () => {
      if (typeof window !== 'undefined' && !window.YT) {
        const tag = document.createElement('script')
        tag.src = 'https://www.youtube.com/iframe_api'
        const firstScriptTag = document.getElementsByTagName('script')[0]
        firstScriptTag.parentNode?.insertBefore(tag, firstScriptTag)
      }
    }

    initYouTubeAPI()

    // Check for queue ID in URL parameters
    const checkURLForQueue = async () => {
      const urlParams = new URLSearchParams(window.location.search)
      const queueId = urlParams.get('q')

      if (queueId) {
        console.log('🔗 Found queue ID in URL:', queueId)
        try {
          const publicQueue = await firebaseService.getPublicQueue(queueId)
          if (publicQueue) {
            loadQueue(publicQueue.queueData)
            console.log('✅ Queue loaded from URL:', publicQueue.metadata.title)

            // Clean up URL without refreshing
            window.history.replaceState({}, document.title, window.location.pathname)
          } else {
            console.warn('Queue not found:', queueId)
          }
        } catch (error) {
          console.error('Failed to load queue from URL:', error)
        }
      }
    }

    checkURLForQueue()
  }, [])

  const renderActiveView = () => {
    switch (activeView) {
      case 'search':
        return <SearchView />
      case 'personal':
        return <PersonalQueuesView />
      case 'public':
        return <PublicQueuesView />
      default:
        return <SearchView />
    }
  }

  return (
    <MainLayout>
      {/* Media Control Center - Video Player and Current Queue */}
      <div
        className={`media-control-center transition-all duration-500 ease-in-out ${
          isMinimized
            ? 'opacity-0 max-h-0 mb-0 overflow-hidden pointer-events-none transform -translate-y-4'
            : 'opacity-100 mb-8 transform translate-y-0'
        }`}
      >
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          <VideoPlayer />
          <CurrentQueue />
        </div>

        {/* Timeframe Display - Full Width Below Player and Queue */}
        <TimeframeDisplay />
      </div>

      {/* Main Content Views */}
      <div className="content-area">
        {renderActiveView()}
      </div>
    </MainLayout>
  )
}
